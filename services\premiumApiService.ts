/**
 * Service pour les requêtes Premium OpenRouter
 * Gère l'envoi de messages via les modèles Premium avec la clé API utilisateur
 * Intégré avec le système de détection automatique des modèles (Août 2025)
 */

import { OPENROUTER_API_URL, LANGUAGE_VALIDATION_CONFIG, PREMIUM_CONFIG, PREMIUM_MODELS_BY_TASK } from '../constants';
import { premiumAuthService } from './premiumAuthService';
import { translationService } from './translationService';
import type { Step } from '../types';

// Import conditionnel pour éviter les dépendances circulaires
let languageValidationService: any;
let autoModelUpdateService: any;

// Chargement différé des services
const loadServices = async () => {
  if (!languageValidationService) {
    try {
      const langModule = await import('./languageValidationService');
      languageValidationService = langModule.languageValidationService;
    } catch (error) {
      console.warn('⚠️ Service de validation linguistique non disponible:', error);
    }
  }
  
  if (!autoModelUpdateService) {
    try {
      const autoModule = await import('./autoModelUpdateService');
      autoModelUpdateService = autoModule.autoModelUpdateService;
    } catch (error) {
      console.warn('⚠️ Service de mise à jour automatique non disponible:', error);
    }
  }
};

interface LanguageValidationResult {
  isValid: boolean;
  confidence: number;
  detectedLanguage?: string;
  suggestions?: string[];
}

interface PremiumApiResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class PremiumApiService {
  /**
   * Envoie un message aux modèles Premium OpenRouter
   */
  public async sendMessageToPremiumAI(
    messages: { role: string; content: string }[],
    task: Step['task']
  ): Promise<{ content: string; modelUsed: string; languageValidation?: LanguageValidationResult }> {
    
    // Charger les services de manière asynchrone
    await loadServices();
    
    const auth = premiumAuthService.getCurrentAuth();
    
    if (!auth.user.isAuthenticated || !auth.user.apiKey) {
      throw new Error('Utilisateur non authentifié en mode Premium');
    }

    // Vérifier les crédits
    if (!premiumAuthService.hasEnoughCredits()) {
      const creditsStatus = premiumAuthService.getCreditsStatus();
      throw new Error(`Crédits insuffisants (${creditsStatus.credits}$). Rechargez votre compte OpenRouter.`);
    }

    // Obtenir les modèles recommandés pour cette tâche
    const recommendedModels = premiumAuthService.getModelsForTask(task);
    
    if (recommendedModels.length === 0) {
      throw new Error(`Aucun modèle Premium disponible pour la tâche: ${task}`);
    }

    let lastError: Error | null = null;

    // Essayer chaque modèle recommandé
    for (const modelId of recommendedModels) {
      try {
        console.log(`🚀 Tentative Premium avec le modèle: ${modelId}`);

        const response = await fetch(OPENROUTER_API_URL, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${auth.user.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin,
            'X-Title': 'Studio Agentique Roony - Premium Mode'
          },
          body: JSON.stringify({
            model: modelId,
            messages: messages,
            temperature: 0.7,
            max_tokens: 4000,
            top_p: 0.9
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json() as PremiumApiResponse;
        
        if (!data.choices || data.choices.length === 0) {
          throw new Error('Réponse vide du modèle Premium');
        }

        let content = data.choices[0].message.content;

        // Validation et traduction automatique si nécessaire
        let languageValidation: LanguageValidationResult | undefined;
        
        if (languageValidationService && LANGUAGE_VALIDATION_CONFIG.ENABLE_STRICT_VALIDATION) {
          languageValidation = languageValidationService.validateFrenchResponse(content, modelId);
          
          if (languageValidation.detectedLanguage !== 'french' && LANGUAGE_VALIDATION_CONFIG.ENABLE_AUTO_TRANSLATION) {
            console.log(`🔄 Traduction automatique nécessaire pour le modèle Premium: ${modelId}`);
            
            try {
              const translationResult = await translationService.translateToFrench(
                content, 
                (languageValidation.detectedLanguage as "french" | "english" | "mixed" | "unknown") || 'unknown',
                languageValidation.confidence || 0
              );
              
              if (translationResult.wasTranslated) {
                content = translationResult.translatedText;
                console.log(`✅ Traduction automatique réussie pour: ${modelId}`);
              }
            } catch (translationError) {
              console.warn(`⚠️ Échec de la traduction pour ${modelId}:`, translationError);
              // Continuer avec le texte original si la traduction échoue
            }
          }
        }

        // Loguer l'utilisation pour les statistiques
        if (data.usage) {
          console.log(`📊 Utilisation Premium - ${modelId}: ${data.usage.total_tokens} tokens (${data.usage.prompt_tokens} prompt + ${data.usage.completion_tokens} completion)`);
        }

        console.log(`✅ Succès Premium avec le modèle: ${modelId}`);
        return { 
          content, 
          modelUsed: modelId,
          languageValidation 
        };

      } catch (error) {
        console.warn(`❌ Erreur Premium avec le modèle ${modelId}:`, error);
        lastError = error instanceof Error ? error : new Error(String(error));
        continue;
      }
    }

    // Si tous les modèles ont échoué
    throw new Error(`Tous les modèles Premium ont échoué. Dernière erreur: ${lastError?.message}`);
  }

  /**
   * Teste la connectivité avec un modèle Premium spécifique
   */
  public async testPremiumModel(modelId: string): Promise<boolean> {
    const auth = premiumAuthService.getCurrentAuth();
    
    if (!auth.user.isAuthenticated || !auth.user.apiKey) {
      return false;
    }

    try {
      const testMessages = [
        { role: 'user', content: 'Réponds simplement "Test réussi" en français.' }
      ];

      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${auth.user.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Studio Agentique Roony - Test Premium'
        },
        body: JSON.stringify({
          model: modelId,
          messages: testMessages,
          temperature: 0.1,
          max_tokens: 50
        })
      });

      return response.ok;
    } catch (error) {
      console.warn(`❌ Test Premium échoué pour ${modelId}:`, error);
      return false;
    }
  }

  /**
   * Obtient les informations détaillées sur un modèle Premium
   */
  public getPremiumModelInfo(modelId: string): { name: string; pricing: string; context: string } | null {
    const availableModels = premiumAuthService.getAvailableModels();
    const model = availableModels.find(m => m.id === modelId);
    
    if (!model) return null;

    return {
      name: model.name,
      pricing: `${(model.pricing.prompt * 1000000).toFixed(3)}$ / ${(model.pricing.completion * 1000000).toFixed(3)}$ par million de tokens`,
      context: `${model.context_length.toLocaleString()} tokens`
    };
  }

  /**
   * Vérifie si le mode Premium est disponible
   */
  public isPremiumAvailable(): boolean {
    const auth = premiumAuthService.getCurrentAuth();
    // Seulement vérifier l'authentification - les autres vérifications peuvent être faites au moment de l'utilisation
    return auth.user.isAuthenticated && !!auth.user.apiKey;
  }

  /**
   * Retourne le statut détaillé du mode Premium avec données de mise à jour automatique
   */
  public getPremiumStatus(): {
    isAvailable: boolean;
    modelsCount: number;
    creditsStatus: { credits: number; isLow: boolean };
    isAuthenticated: boolean;
    autoUpdateInfo: {
      lastUpdate: Date;
      nextUpdate: Date;
      daysUntilNextUpdate: number;
      availablePremiumModels: number;
    };
  } {
    const auth = premiumAuthService.getCurrentAuth();
    const creditsStatus = premiumAuthService.getCreditsStatus();
    const modelsCount = premiumAuthService.getAvailableModels().length;
    
    // Données par défaut si le service de mise à jour automatique n'est pas disponible
    const defaultUpdateInfo = {
      lastUpdate: new Date(),
      nextUpdate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // +7 jours
      daysUntilNextUpdate: 7,
      availablePremiumModels: modelsCount
    };

    let updateInfo = defaultUpdateInfo;
    
    // Essayer d'obtenir les info de mise à jour si le service est disponible
    if (autoModelUpdateService) {
      try {
        const updateStatus = autoModelUpdateService.getUpdateStatus();
        
        // Compter les modèles premium disponibles via le service de mise à jour automatique
        const availablePremiumModels = ['analyse', 'génération', 'validation', 'synthèse']
          .reduce((total, task) => 
            total + (autoModelUpdateService.getPremiumModelsForTask(task as Step['task'])?.length || 0), 0);

        updateInfo = {
          lastUpdate: updateStatus.lastUpdate,
          nextUpdate: updateStatus.nextUpdate,
          daysUntilNextUpdate: updateStatus.daysUntilNextUpdate,
          availablePremiumModels
        };
      } catch (error) {
        console.warn('⚠️ Impossible d\'obtenir les info de mise à jour automatique:', error);
      }
    }

    return {
      isAvailable: this.isPremiumAvailable(),
      modelsCount,
      creditsStatus,
      isAuthenticated: auth.user.isAuthenticated,
      autoUpdateInfo: updateInfo
    };
  }

  /**
   * Obtient les modèles premium recommandés pour une tâche avec mise à jour automatique
   */
  public getPremiumModelsForTask(task: Step['task']): string[] {
    // Utiliser le service de mise à jour automatique pour obtenir les modèles les plus récents
    if (autoModelUpdateService) {
      try {
        const autoDetectedModels = autoModelUpdateService.getPremiumModelsForTask(task);
        
        if (autoDetectedModels.length > 0) {
          // Retourner les IDs des modèles détectés automatiquement
          return autoDetectedModels.map((model: any) => model.id).slice(0, 5); // Limiter à 5 pour les performances
        }
      } catch (error) {
        console.warn('⚠️ Impossible d\'obtenir les modèles via le service automatique:', error);
      }
    }
    
    // Fallback vers la méthode statique si le service automatique n'a pas de données
    return premiumAuthService.getModelsForTask(task);
  }

  /**
   * Force une mise à jour des modèles premium et retourne les nouveaux modèles disponibles
   */
  public async updatePremiumModels(): Promise<{
    success: boolean;
    newModelsCount: number;
    totalPremiumModels: number;
    error?: string;
  }> {
    if (!autoModelUpdateService) {
      return {
        success: false,
        newModelsCount: 0,
        totalPremiumModels: 0,
        error: 'Service de mise à jour automatique non disponible'
      };
    }
    
    try {
      console.log('🔄 Mise à jour forcée des modèles premium...');
      const report = await autoModelUpdateService.performUpdate(true);
      
      const totalPremiumModels = ['analyse', 'génération', 'validation', 'synthèse']
        .reduce((total, task) => 
          total + (autoModelUpdateService.getPremiumModelsForTask(task as Step['task'])?.length || 0), 0);

      console.log(`✅ Mise à jour premium terminée: ${report.newPremiumModels?.length || 0} nouveaux modèles, ${totalPremiumModels} total`);

      return {
        success: true,
        newModelsCount: report.newPremiumModels?.length || 0,
        totalPremiumModels
      };
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour des modèles premium:', error);
      return {
        success: false,
        newModelsCount: 0,
        totalPremiumModels: 0,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      };
    }
  }
}

// Instance singleton
export const premiumApiService = new PremiumApiService();
