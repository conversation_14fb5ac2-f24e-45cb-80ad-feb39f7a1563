/**
 * Script de diagnostic pour le mode Premium
 * Vérifie tous les composants nécessaires au fonctionnement du mode Premium
 */

console.log('🔍 === DIAGNOSTIC MODE PREMIUM ===');

// 1. Vérifier la disponibilité des services
console.log('\n📦 Vérification des services...');

try {
  const { premiumAuthService } = require('./services/premiumAuthService');
  console.log('✅ premiumAuthService: Disponible');
  
  // Test de l'état d'authentification
  const auth = premiumAuthService.getCurrentAuth();
  console.log(`   - Authentifié: ${auth.user.isAuthenticated}`);
  console.log(`   - Plan: ${auth.user.plan}`);
  console.log(`   - API Key: ${auth.user.apiKey ? 'Présente' : 'Absente'}`);
} catch (error) {
  console.error('❌ premiumAuthService: Erreur -', error.message);
}

try {
  const { premiumApiService } = require('./services/premiumApiService');
  console.log('✅ premiumApiService: Disponible');
  
  // Test de disponibilité Premium
  const isAvailable = premiumApiService.isPremiumAvailable();
  console.log(`   - Mode Premium disponible: ${isAvailable}`);
  
  const status = premiumApiService.getPremiumStatus();
  console.log(`   - Nombre de modèles: ${status.modelsCount}`);
  console.log(`   - Crédits: ${status.creditsStatus.credits}$`);
} catch (error) {
  console.error('❌ premiumApiService: Erreur -', error.message);
}

try {
  const { usageModeService } = require('./services/usageModeService');
  console.log('✅ usageModeService: Disponible');
  
  const modeInfo = usageModeService.getModeInfo();
  console.log(`   - Mode actuel: ${modeInfo.currentMode}`);
  console.log(`   - Peut utiliser Premium: ${modeInfo.canUsePremium}`);
  console.log(`   - Configuration optimale: ${modeInfo.isOptimal}`);
} catch (error) {
  console.error('❌ usageModeService: Erreur -', error.message);
}

// 2. Vérifier les services optionnels
console.log('\n🔧 Vérification des services optionnels...');

try {
  const { autoModelUpdateService } = require('./services/autoModelUpdateService');
  console.log('✅ autoModelUpdateService: Disponible');
} catch (error) {
  console.warn('⚠️ autoModelUpdateService: Non disponible -', error.message);
}

try {
  const { languageValidationService } = require('./services/languageValidationService');
  console.log('✅ languageValidationService: Disponible');
} catch (error) {
  console.warn('⚠️ languageValidationService: Non disponible -', error.message);
}

// 3. Vérifier la configuration
console.log('\n⚙️ Vérification de la configuration...');

try {
  const { PREMIUM_CONFIG, PREMIUM_MODELS_BY_TASK } = require('./constants');
  console.log('✅ Configuration Premium: Chargée');
  console.log(`   - URL des modèles: ${PREMIUM_CONFIG.MODELS_API_URL}`);
  console.log(`   - Prix min/max: ${PREMIUM_CONFIG.PRICE_FILTER.MIN_PRICE}$ - ${PREMIUM_CONFIG.PRICE_FILTER.MAX_PRICE}$`);
  console.log(`   - Tâches configurées: ${Object.keys(PREMIUM_MODELS_BY_TASK).length}`);
} catch (error) {
  console.error('❌ Configuration Premium: Erreur -', error.message);
}

// 4. Vérifier le localStorage
console.log('\n💾 Vérification du stockage local...');

try {
  const authData = localStorage.getItem('roony_premium_auth');
  console.log(`✅ Données d'auth Premium: ${authData ? 'Présentes' : 'Absentes'}`);
  
  const modelsCache = localStorage.getItem('roony_premium_models');
  console.log(`✅ Cache des modèles: ${modelsCache ? 'Présent' : 'Absent'}`);
  
  const usageMode = localStorage.getItem('roony_usage_mode');
  console.log(`✅ Mode d'utilisation: ${usageMode || 'Défaut (free)'}`);
} catch (error) {
  console.error('❌ Stockage local: Erreur -', error.message);
}

// 5. Recommendations
console.log('\n💡 Recommandations:');

const { premiumAuthService } = require('./services/premiumAuthService');
const auth = premiumAuthService.getCurrentAuth();

if (!auth.user.isAuthenticated) {
  console.log('🔸 Pour activer le mode Premium:');
  console.log('   1. Obtenez une clé API sur https://openrouter.ai');
  console.log('   2. Ajoutez des crédits à votre compte');
  console.log('   3. Connectez-vous via l\'interface Premium');
} else if (auth.user.isAuthenticated) {
  const { premiumApiService } = require('./services/premiumApiService');
  const status = premiumApiService.getPremiumStatus();
  
  if (status.creditsStatus.isLow) {
    console.log('🔸 Crédits faibles - Rechargez votre compte OpenRouter');
  }
  
  if (status.modelsCount === 0) {
    console.log('🔸 Aucun modèle Premium disponible - Vérifiez la connexion API');
  }
  
  if (status.isAvailable) {
    console.log('🔸 Mode Premium prêt à l\'utilisation ! 🎉');
  }
}

console.log('\n🔍 === FIN DU DIAGNOSTIC ===');

export {}; // Pour faire de ce fichier un module
