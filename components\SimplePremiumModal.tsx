/**
 * Modal simple et direct pour l'authentification Premium OpenRouter
 * Solution simplifiée pour résoudre les problèmes de modal bloqué
 */

import React, { useState, useEffect } from 'react';
import { premiumAuthService } from '../services/premiumAuthService';
import type { AuthenticationState } from '../types';

interface SimplePremiumModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  onFreeMode?: () => void; // Callback pour choisir le mode gratuit
  isRequired?: boolean; // Si true, le modal ne peut pas être fermé
}

export const SimplePremiumModal: React.FC<SimplePremiumModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onFreeMode,
  isRequired = false
}) => {
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [authState, setAuthState] = useState<AuthenticationState>({
    user: { isAuthenticated: false, plan: 'free' },
    isLoading: false,
    error: null
  });

  // Écouter les changements d'authentification
  useEffect(() => {
    const unsubscribe = premiumAuthService.onAuthChange((auth) => {
      setAuthState(auth);
      setIsLoading(auth.isLoading);
      setError(auth.error || '');
      
      // Si l'authentification réussit, fermer le modal
      if (auth.user.isAuthenticated && !auth.isLoading) {
        onSuccess?.();
        onClose();
      }
    });

    return unsubscribe;
  }, [onClose, onSuccess]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      setError('Veuillez entrer votre clé API OpenRouter');
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      const success = await premiumAuthService.authenticateUser(apiKey.trim());
      
      if (success) {
        console.log('🌟 Authentification Premium réussie !');
        setApiKey(''); // Effacer le champ
        // Le modal se fermera automatiquement via l'effet useEffect
      } else {
        // L'erreur sera gérée par le service et transmise via onAuthChange
        console.error('❌ Échec de l\'authentification Premium');
      }
    } catch (err) {
      console.error('❌ Erreur lors de l\'authentification:', err);
      setError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setApiKey('');
    setError('');
    setIsLoading(false);
    onClose();
  };

  const handleFreeMode = () => {
    console.log('🆓 Utilisateur choisit le mode gratuit');
    onFreeMode?.();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-slate-800/95 border border-slate-600 rounded-2xl p-6 max-w-lg w-full mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              🎯 {isRequired ? 'Choisissez votre mode d\'utilisation' : 'Mode Premium'}
            </h2>
            <p className="text-slate-300 text-sm mt-1">
              {isRequired
                ? 'Sélectionnez le mode qui vous convient le mieux pour utiliser Roony'
                : 'Connectez-vous avec votre clé API OpenRouter'
              }
            </p>
          </div>
          {!isRequired && (
            <button
              onClick={handleClose}
              className="text-slate-400 hover:text-white transition-colors text-xl"
              disabled={isLoading}
            >
              ✕
            </button>
          )}
        </div>

        {/* Message d'information si authentification requise */}
        {isRequired && (
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <span className="text-blue-400 text-lg">ℹ️</span>
              <div>
                <h4 className="text-blue-300 font-medium mb-1">Deux options s'offrent à vous :</h4>
                <p className="text-blue-200 text-sm">
                  <strong>Mode Premium :</strong> Utilisez votre clé API OpenRouter pour accéder aux modèles les plus performants avec vos propres crédits.
                  <br /><br />
                  <strong>Mode Gratuit :</strong> Utilisez Roony immédiatement avec les modèles gratuits disponibles.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-slate-300 mb-2">
              Clé API OpenRouter:
            </label>
            <div className="relative">
              <input
                id="apiKey"
                type={showApiKey ? 'text' : 'password'}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-or-v1-..."
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent pr-12"
                disabled={isLoading}
                autoComplete="off"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                disabled={isLoading}
              >
                {showApiKey ? '👁️‍🗨️' : '👁️'}
              </button>
            </div>
          </div>

          {/* Message d'erreur */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm flex items-center gap-2">
                ❌ {error}
              </p>
            </div>
          )}

          {/* Boutons d'action */}
          <div className="space-y-3">
            <button
              type="submit"
              disabled={isLoading || !apiKey.trim()}
              className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:from-slate-600 disabled:to-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connexion en cours...
                </span>
              ) : (
                'Se connecter au mode Premium'
              )}
            </button>

            {/* Bouton mode gratuit */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-slate-600"></div>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="bg-slate-800 px-2 text-slate-400">ou</span>
              </div>
            </div>

            <button
              type="button"
              onClick={handleFreeMode}
              disabled={isLoading}
              className="w-full bg-slate-700/50 hover:bg-slate-600/50 disabled:bg-slate-700/30 text-slate-300 font-medium py-3 px-4 rounded-lg transition-all duration-200 disabled:cursor-not-allowed border border-slate-600 hover:border-slate-500"
            >
              🆓 Rester en mode gratuit
            </button>
          </div>
        </form>

        {/* Informations supplémentaires */}
        <div className="mt-6 pt-6 border-t border-slate-600">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">🎯 Mode Premium:</h4>
              <ul className="text-xs text-slate-400 space-y-1">
                <li>✨ Modèles IA plus puissants (GPT-4o-mini, Claude 3.5 Haiku)</li>
                <li>💰 Prix attractifs (0.1$ à 1$ par million de tokens)</li>
                <li>🔐 Vos propres crédits OpenRouter</li>
                <li>🚀 Performances optimisées</li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">🆓 Mode Gratuit:</h4>
              <ul className="text-xs text-slate-400 space-y-1">
                <li>🤖 Modèles IA gratuits disponibles</li>
                <li>💸 Aucun coût pour vous</li>
                <li>⚡ Accès immédiat sans configuration</li>
                <li>🎯 Parfait pour découvrir Roony</li>
              </ul>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-slate-400 mb-2">
                Pas encore de compte OpenRouter ?
              </p>
              <div className="space-y-2">
                <a
                  href="https://openrouter.ai/signup"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-purple-400 hover:text-purple-300 text-xs underline transition-colors"
                >
                  1. Créer un compte OpenRouter →
                </a>
                <a
                  href="https://openrouter.ai/keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-purple-400 hover:text-purple-300 text-xs underline transition-colors"
                >
                  2. Générer votre clé API →
                </a>
              </div>

              {/* Bouton de debug pour réinitialiser le choix (seulement en développement) */}
              {process.env.NODE_ENV === 'development' && (
                <button
                  onClick={() => {
                    localStorage.removeItem('roony_user_choice');
                    window.location.reload();
                  }}
                  className="mt-4 text-xs text-slate-500 hover:text-slate-400 underline transition-colors"
                >
                  🔧 Dev: Réinitialiser le choix
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplePremiumModal;
