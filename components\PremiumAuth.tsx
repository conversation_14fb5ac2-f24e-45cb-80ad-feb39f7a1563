/**
 * Composant d'authentification pour le mode Premium OpenRouter
 * Permet aux utilisateurs de se connecter avec leur clé API personnelle
 */

import React, { useState, useEffect } from 'react';
import { premiumAuthService } from '../services/premiumAuthService';
import type { AuthenticationState } from '../types';

interface PremiumAuthProps {
  onAuthSuccess?: () => void;
  onAuthError?: (error: string) => void;
}

const PremiumAuth: React.FC<PremiumAuthProps> = ({ onAuthSuccess, onAuthError }) => {
  const [authState, setAuthState] = useState<AuthenticationState>({
    user: { isAuthenticated: false, plan: 'free' },
    isLoading: false
  });
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    // S'abonner aux changements d'authentification
    const unsubscribe = premiumAuthService.onAuthChange(setAuthState);
    return unsubscribe;
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      const error = 'Veuillez entrer votre clé API OpenRouter';
      setAuthState(prev => ({ ...prev, error }));
      onAuthError?.(error);
      return;
    }

    const success = await premiumAuthService.authenticateUser(apiKey.trim());
    
    if (success) {
      setApiKey(''); // Effacer le champ après succès
      onAuthSuccess?.();
    } else {
      // L'erreur est gérée par le service et transmise via onAuthChange
      onAuthError?.(authState.error || 'Erreur d\'authentification');
    }
  };

  const handleLogout = () => {
    premiumAuthService.logout();
    setApiKey('');
  };

  const creditsStatus = premiumAuthService.getCreditsStatus();

  if (authState.user.isAuthenticated) {
    return (
      <div className="premium-auth-container authenticated">
        <div className="premium-status">
          <div className="premium-header">
            <h3>🌟 Mode Premium Activé</h3>
            <button 
              onClick={handleLogout}
              className="logout-btn"
              title="Se déconnecter du mode Premium"
            >
              Déconnexion
            </button>
          </div>
          
          <div className="premium-info">
            <div className="credits-info">
              <span className="credits-label">Crédits:</span>
              <span className={`credits-value ${creditsStatus.isLow ? 'low' : ''}`}>
                {creditsStatus.credits.toFixed(2)}$
              </span>
              {creditsStatus.isLow && (
                <span className="credits-warning">⚠️ Crédits faibles</span>
              )}
            </div>
            
            <div className="models-info">
              <span className="models-count">
                {premiumAuthService.getAvailableModels().length} modèles premium disponibles
              </span>
            </div>
          </div>

          {creditsStatus.isLow && (
            <div className="low-credits-alert">
              <p>💳 Vos crédits sont faibles. Rechargez votre compte OpenRouter pour continuer à utiliser le mode Premium.</p>
              <a 
                href="https://openrouter.ai/credits" 
                target="_blank" 
                rel="noopener noreferrer"
                className="recharge-link"
              >
                Recharger mes crédits →
              </a>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="premium-auth-container">
      <div className="premium-login">
        <div className="premium-header">
          <h3>🌟 Mode Premium</h3>
          <p>Connectez-vous avec votre clé API OpenRouter pour accéder aux modèles premium</p>
        </div>

        <form onSubmit={handleLogin} className="login-form">
          <div className="api-key-field">
            <label htmlFor="apiKey">Clé API OpenRouter:</label>
            <div className="input-group">
              <input
                id="apiKey"
                type={showApiKey ? 'text' : 'password'}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-or-v1-..."
                className="api-key-input"
                disabled={authState.isLoading}
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="toggle-visibility-btn"
                title={showApiKey ? 'Masquer' : 'Afficher'}
              >
                {showApiKey ? '👁️‍🗨️' : '👁️'}
              </button>
            </div>
          </div>

          {authState.error && (
            <div className="error-message">
              ❌ {authState.error}
            </div>
          )}

          <button 
            type="submit" 
            className="login-btn"
            disabled={authState.isLoading || !apiKey.trim()}
          >
            {authState.isLoading ? 'Connexion...' : 'Se connecter'}
          </button>
        </form>

        <div className="premium-benefits">
          <h4>🎯 Avantages du mode Premium:</h4>
          <ul>
            <li>✨ Modèles IA plus puissants (GPT-4o-mini, Claude 3.5 Haiku, etc.)</li>
            <li>💰 Prix attractifs (0.1$ à 1$ par million de tokens)</li>
            <li>🔐 Vos propres crédits OpenRouter</li>
            <li>🚀 Performances optimisées pour chaque tâche</li>
            <li>📊 Suivi de l'utilisation en temps réel</li>
          </ul>
        </div>

        <div className="openrouter-info">
          <p>
            <strong>Pas encore de compte OpenRouter ?</strong>
          </p>
          <a 
            href="https://openrouter.ai/signup" 
            target="_blank" 
            rel="noopener noreferrer"
            className="signup-link"
          >
            Créer un compte OpenRouter →
          </a>
        </div>
      </div>

      <style jsx>{`
        .premium-auth-container {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
          border-radius: 12px;
          padding: 24px;
          margin: 16px 0;
          color: white;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(102, 126, 234, 0.3);
          backdrop-filter: blur(10px);
        }

        .premium-auth-container.authenticated {
          background: linear-gradient(135deg, rgba(17, 153, 142, 0.8) 0%, rgba(56, 239, 125, 0.8) 100%);
          border: 1px solid rgba(17, 153, 142, 0.3);
        }

        .premium-header h3 {
          margin: 0 0 8px 0;
          font-size: 1.4em;
          font-weight: 600;
          color: #f1f5f9;
        }

        .premium-header p {
          margin: 0 0 20px 0;
          opacity: 0.9;
          font-size: 0.95em;
          color: #e2e8f0;
        }

        .premium-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .logout-btn {
          background: rgba(30, 41, 59, 0.3);
          color: white;
          border: 1px solid rgba(100, 116, 139, 0.4);
          padding: 6px 12px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.85em;
          transition: all 0.2s ease;
        }

        .logout-btn:hover {
          background: rgba(30, 41, 59, 0.5);
          border-color: rgba(100, 116, 139, 0.6);
        }

        .login-form {
          margin-bottom: 24px;
        }

        .api-key-field label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          font-size: 0.95em;
          color: #f1f5f9;
        }

        .input-group {
          display: flex;
          gap: 8px;
          margin-bottom: 16px;
        }

        .api-key-input {
          flex: 1;
          padding: 12px;
          border: 1px solid rgba(100, 116, 139, 0.4);
          border-radius: 8px;
          background: rgba(30, 41, 59, 0.4);
          color: #f1f5f9;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        .api-key-input::placeholder {
          color: rgba(226, 232, 240, 0.6);
        }

        .api-key-input:focus {
          outline: none;
          border-color: rgba(100, 116, 139, 0.7);
          background: rgba(30, 41, 59, 0.6);
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .toggle-visibility-btn {
          background: rgba(30, 41, 59, 0.3);
          color: white;
          border: 1px solid rgba(100, 116, 139, 0.4);
          padding: 8px 12px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .toggle-visibility-btn:hover {
          background: rgba(30, 41, 59, 0.5);
        }

        .error-message {
          background: rgba(239, 68, 68, 0.2);
          border: 1px solid rgba(239, 68, 68, 0.3);
          padding: 10px;
          border-radius: 6px;
          margin-bottom: 16px;
          font-size: 0.9em;
          color: #fecaca;
        }

        .login-btn {
          width: 100%;
          background: rgba(30, 41, 59, 0.3);
          color: white;
          border: 1px solid rgba(100, 116, 139, 0.4);
          padding: 12px;
          border-radius: 8px;
          font-size: 1em;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .login-btn:hover:not(:disabled) {
          background: rgba(30, 41, 59, 0.5);
          border-color: rgba(100, 116, 139, 0.6);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .login-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .premium-benefits h4 {
          margin: 0 0 12px 0;
          font-size: 1.1em;
          color: #f1f5f9;
        }

        .premium-benefits ul {
          margin: 0;
          padding-left: 20px;
        }

        .premium-benefits li {
          margin-bottom: 6px;
          font-size: 0.9em;
          opacity: 0.95;
          color: #e2e8f0;
        }

        .openrouter-info {
          margin-top: 20px;
          padding-top: 20px;
          border-top: 1px solid rgba(100, 116, 139, 0.3);
          text-align: center;
        }

        .openrouter-info p {
          color: #e2e8f0;
        }

        .signup-link, .recharge-link {
          color: white;
          text-decoration: none;
          font-weight: 500;
          padding: 8px 16px;
          border: 1px solid rgba(100, 116, 139, 0.4);
          border-radius: 6px;
          display: inline-block;
          margin-top: 8px;
          transition: all 0.2s ease;
          background: rgba(30, 41, 59, 0.3);
        }

        .signup-link:hover, .recharge-link:hover {
          background: rgba(30, 41, 59, 0.5);
          border-color: rgba(100, 116, 139, 0.6);
        }

        .premium-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          flex-wrap: wrap;
          gap: 12px;
        }

        .credits-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .credits-value {
          font-weight: 600;
          font-size: 1.1em;
          color: #f1f5f9;
        }

        .credits-value.low {
          color: #fbbf24;
        }

        .credits-warning {
          font-size: 0.8em;
          background: rgba(251, 191, 36, 0.2);
          padding: 2px 6px;
          border-radius: 4px;
          color: #fbbf24;
        }

        .models-info {
          font-size: 0.9em;
          opacity: 0.9;
          color: #e2e8f0;
        }

        .low-credits-alert {
          background: rgba(251, 191, 36, 0.2);
          border: 1px solid rgba(251, 191, 36, 0.4);
          padding: 12px;
          border-radius: 8px;
          text-align: center;
        }

        .low-credits-alert p {
          margin: 0 0 8px 0;
          font-size: 0.9em;
          color: #fbbf24;
        }
      `}</style>
    </div>
  );
};

export default PremiumAuth;
