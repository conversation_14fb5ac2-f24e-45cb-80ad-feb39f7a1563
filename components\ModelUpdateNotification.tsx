import React, { useState, useEffect } from 'react';
import { autoModelUpdateService, type ModelUpdateReport } from '../services/autoModelUpdateService';

interface ModelUpdateNotificationProps {
  className?: string;
}

const ModelUpdateNotification: React.FC<ModelUpdateNotificationProps> = ({ className = '' }) => {
  const [updateReport, setUpdateReport] = useState<ModelUpdateReport | null>(null);
  const [showNotification, setShowNotification] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<{
    lastUpdate: Date;
    nextUpdate: Date;
    daysUntilNextUpdate: number;
    modelsCount: number;
  } | null>(null);

  useEffect(() => {
    // Charger le statut de mise à jour
    const status = autoModelUpdateService.getUpdateStatus();
    setUpdateStatus(status);

    // S'abonner aux mises à jour
    const handleUpdate = (report: ModelUpdateReport) => {
      console.log('🔔 Nouvelle mise à jour des modèles reçue:', report);
      setUpdateReport(report);
      
      // Afficher la notification s'il y a des nouveaux modèles significatifs
      const totalNewModels = report.newFreeModels.length + report.newPremiumModels.length;
      if (totalNewModels >= 3) {
        setShowNotification(true);
        
        // Auto-masquer après 10 secondes
        setTimeout(() => {
          setShowNotification(false);
        }, 10000);
      }
    };

    autoModelUpdateService.onUpdate(handleUpdate);

    // Vérifier périodiquement le statut
    const statusInterval = setInterval(() => {
      const newStatus = autoModelUpdateService.getUpdateStatus();
      setUpdateStatus(newStatus);
    }, 60000); // Toutes les minutes

    return () => {
      clearInterval(statusInterval);
    };
  }, []);

  const handleDismissNotification = () => {
    setShowNotification(false);
  };

  const handleForceUpdate = async () => {
    try {
      const report = await autoModelUpdateService.performUpdate(true);
      setUpdateReport(report);
      setShowNotification(true);
      
      setTimeout(() => {
        setShowNotification(false);
      }, 8000);
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour forcée:', error);
    }
  };

  if (!updateStatus) return null;

  return (
    <div className={`bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-slate-300 flex items-center">
          🤖 Modèles IA
        </h3>
        <button
          onClick={handleForceUpdate}
          className="text-xs px-2 py-1 bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors"
          title="Forcer la mise à jour"
        >
          🔄 MAJ
        </button>
      </div>

      <div className="space-y-2 text-xs text-slate-400">
        <div className="flex justify-between">
          <span>Total disponible:</span>
          <span className="text-blue-400 font-medium">{updateStatus.modelsCount}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Dernière MAJ:</span>
          <span className="text-slate-300">
            {updateStatus.lastUpdate.toLocaleDateString('fr-FR')}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Prochaine MAJ:</span>
          <span className="text-slate-300">
            {updateStatus.daysUntilNextUpdate === 0 
              ? 'Aujourd\'hui' 
              : `${updateStatus.daysUntilNextUpdate}j`}
          </span>
        </div>
      </div>

      {/* Notification de nouveaux modèles */}
      {showNotification && updateReport && (
        <div className="fixed top-4 right-4 z-50 bg-gradient-to-r from-green-600 to-blue-600 text-white p-4 rounded-xl shadow-2xl border border-green-500/30 max-w-md animate-slide-in">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <span className="text-lg mr-2">🎉</span>
                <h4 className="font-bold text-sm">Nouveaux modèles IA détectés !</h4>
              </div>
              
              <div className="space-y-1 text-xs">
                {updateReport.newFreeModels.length > 0 && (
                  <div className="flex items-center">
                    <span className="text-green-300 mr-2">💝</span>
                    <span>{updateReport.newFreeModels.length} nouveaux modèles gratuits</span>
                  </div>
                )}
                
                {updateReport.newPremiumModels.length > 0 && (
                  <div className="flex items-center">
                    <span className="text-yellow-300 mr-2">🌟</span>
                    <span>{updateReport.newPremiumModels.length} nouveaux modèles premium</span>
                  </div>
                )}
                
                <div className="flex items-center mt-2">
                  <span className="text-blue-300 mr-2">📊</span>
                  <span>Total: {updateReport.totalModels} modèles ({updateReport.freeModels} gratuits)</span>
                </div>
              </div>

              {/* Aperçu des nouveaux modèles */}
              {updateReport.newFreeModels.slice(0, 3).map((model, index) => (
                <div key={model.id} className="text-xs text-green-200 mt-1 opacity-80">
                  • {model.name.substring(0, 30)}...
                </div>
              ))}
            </div>
            
            <button
              onClick={handleDismissNotification}
              className="text-white/70 hover:text-white ml-2 transition-colors"
            >
              ✕
            </button>
          </div>
          
          <div className="mt-3 text-xs text-white/70">
            Mise à jour automatique - Studio Agentique Roony
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        .animate-slide-in {
          animation: slide-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default ModelUpdateNotification;
